import React, { useEffect, useRef } from "react";
import gsap from "gsap";
import ScrollTrigger from "gsap/ScrollTrigger";

gsap.registerPlugin(ScrollTrigger);

export default function AboutSection() {
  const sectionRef = useRef(null);
  const textRef = useRef(null);
  const imagesRef = useRef([]);

  useEffect(() => {
    let ctx = gsap.context(() => {
      // Pin the text section
      ScrollTrigger.create({
        trigger: textRef.current,
        start: "top top",
        end: "+=300vh",
        pin: true,
        pinSpacing: false,
      });

      // Animate images to move up and overlay the text
      imagesRef.current.forEach((img, index) => {
        if (img) {
          gsap.fromTo(
            img,
            {
              y: window.innerHeight * 0.8,
              scale: 0.7,
              opacity: 0,
              rotation: 5,
            },
            {
              y: -window.innerHeight * 0.3,
              scale: 1.2,
              opacity: 1,
              rotation: 0,
              ease: "none",
              scrollTrigger: {
                trigger: img,
                start: "top bottom",
                end: "bottom top",
                scrub: 2,
              },
            }
          );
        }
      });
    }, sectionRef);

    return () => ctx.revert();
  }, []);

  return (
    <section
      ref={sectionRef}
      className="bg-[#0a0a0a] text-white font-sansBody relative"
    >
      {/* Pinned Text Section */}
      <div
        ref={textRef}
        className="h-screen flex flex-col justify-center items-center px-6 md:px-24 text-center relative z-10"
      >
        <p className="uppercase text-xs tracking-widest text-[#666666] mb-6 font-sansBody">
          ABOUT ME
        </p>
        <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold max-w-5xl leading-tight font-serifTitle">
          INDIA-BASED INDEPENDENT CREATIVE, SPECIALIZING IN HELPING BUSINESSES
          AND INDIVIDUALS TURN THEIR IDEAS INTO IMPACTFUL DEVELOPMENT AND DESIGN
          SOLUTIONS.
        </h2>
        <span className="text-2xl mt-6">✍️</span>
      </div>

      {/* Scrollable Images that overlay the text */}
      <div className="relative z-50">
        <div className="h-[100vh]"></div> {/* Spacer */}
        {/* Images positioned to scroll over text */}
        <div className="space-y-[50vh]">
          <div className="flex justify-between items-center px-6 md:px-24">
            <img
              ref={(el) => (imagesRef.current[0] = el)}
              src="src/assets/images/about/a1.png"
              alt="Me 1"
              className="w-[200px] h-[250px] object-cover rounded-lg shadow-2xl relative z-50"
            />
            <img
              ref={(el) => (imagesRef.current[1] = el)}
              src="src/assets/images/about/a2.png"
              alt="Me 2"
              className="w-[200px] h-[250px] object-cover rounded-lg shadow-2xl relative z-50"
            />
          </div>

          <div className="flex justify-center px-6 md:px-24">
            <img
              ref={(el) => (imagesRef.current[2] = el)}
              src="src/assets/images/about/a3.png"
              alt="Me 3"
              className="w-[250px] h-[250px] object-cover rounded-lg shadow-2xl relative z-50"
            />
          </div>

          <div className="flex justify-center px-6 md:px-24">
            <img
              ref={(el) => (imagesRef.current[3] = el)}
              src="src/assets/images/about/a4.png"
              alt="Me 4"
              className="w-[300px] h-[400px] object-cover rounded-lg shadow-2xl relative z-50"
            />
          </div>
        </div>
        <div className="h-[100vh]"></div> {/* Bottom spacer */}
      </div>
    </section>
  );
}
