import React, { useEffect, useRef } from "react";
import gsap from "gsap";
import ScrollTrigger from "gsap/ScrollTrigger";

gsap.registerPlugin(ScrollTrigger);

export default function AboutSection() {
  const sectionRef = useRef(null);
  const textRef = useRef(null);
  const imagesRef = useRef([]);

  useEffect(() => {
    let ctx = gsap.context(() => {
      // Pin the text section for the entire image sequence
      ScrollTrigger.create({
        trigger: sectionRef.current,
        start: "top top",
        end: "+=400vh", // Extended duration for all images
        pin: textRef.current,
        pinSpacing: false,
      });

      // Animate each image to scroll over the pinned text
      imagesRef.current.forEach((img, index) => {
        if (img) {
          const delay = index * 100; // Stagger each image

          gsap.fromTo(
            img,
            {
              y: window.innerHeight,
              scale: 0.8,
              opacity: 0,
            },
            {
              y:
                index === imagesRef.current.length - 1
                  ? 0
                  : -window.innerHeight * 0.2, // Last image stops at center
              scale: 1,
              opacity: 1,
              ease: "none",
              scrollTrigger: {
                trigger: sectionRef.current,
                start: `top+=${delay}vh top`,
                end: `top+=${delay + 100}vh top`,
                scrub: 1,
              },
            }
          );
        }
      });
    }, sectionRef);

    return () => ctx.revert();
  }, []);

  return (
    <section
      ref={sectionRef}
      className="bg-[#0a0a0a] text-white font-sansBody relative h-[500vh]"
    >
      {/* Pinned Text Section */}
      <div
        ref={textRef}
        className="h-screen flex flex-col justify-center items-center px-6 md:px-24 text-center relative z-10"
      >
        <p className="uppercase text-xs tracking-widest text-[#666666] mb-6 font-sansBody">
          ABOUT ME
        </p>
        <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold max-w-5xl leading-tight font-serifTitle">
          INDIA-BASED INDEPENDENT CREATIVE, SPECIALIZING IN HELPING BUSINESSES
          AND INDIVIDUALS TURN THEIR IDEAS INTO IMPACTFUL DEVELOPMENT AND DESIGN
          SOLUTIONS.
        </h2>
        <span className="text-2xl mt-6">✍️</span>
      </div>

      {/* Images that will scroll over the pinned text */}
      <div className="absolute inset-0 z-20">
        {/* First two images side by side */}
        <img
          ref={(el) => (imagesRef.current[0] = el)}
          src="src/assets/images/about/a1.png"
          alt="Me 1"
          className="absolute left-[10%] top-1/2 w-[200px] h-[250px] object-cover rounded-lg shadow-2xl"
        />
        <img
          ref={(el) => (imagesRef.current[1] = el)}
          src="src/assets/images/about/a2.png"
          alt="Me 2"
          className="absolute right-[10%] top-1/2 w-[200px] h-[250px] object-cover rounded-lg shadow-2xl"
        />

        {/* Center image */}
        <img
          ref={(el) => (imagesRef.current[2] = el)}
          src="src/assets/images/about/a3.png"
          alt="Me 3"
          className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[250px] h-[250px] object-cover rounded-lg shadow-2xl"
        />

        {/* Final large image */}
        <img
          ref={(el) => (imagesRef.current[3] = el)}
          src="src/assets/images/about/a4.png"
          alt="Me 4"
          className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[300px] h-[400px] object-cover rounded-lg shadow-2xl"
        />
      </div>
    </section>
  );
}
