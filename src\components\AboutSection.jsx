import React, { useEffect, useRef } from "react";
import gsap from "gsap";
import ScrollTrigger from "gsap/ScrollTrigger";

gsap.registerPlugin(ScrollTrigger);

export default function AboutSection() {
  const sectionRef = useRef(null);
  const textRef = useRef(null);
  const imagesRef = useRef([]);

  useEffect(() => {
    let ctx = gsap.context(() => {
      // Create timeline for the entire sequence
      const tl = gsap.timeline({
        scrollTrigger: {
          trigger: sectionRef.current,
          start: "top top",
          end: "bottom bottom",
          scrub: 1,
          pin: textRef.current,
          pinSpacing: false,
        },
      });

      // Set initial states for all images
      gsap.set(imagesRef.current, {
        y: "100vh",
        opacity: 0,
        scale: 0.8,
      });

      // Animate images one by one
      imagesRef.current.forEach((img, index) => {
        if (img) {
          const isLastImage = index === imagesRef.current.length - 1;

          tl.to(
            img,
            {
              y: isLastImage ? "0vh" : "-50vh", // Last image stops at center
              opacity: 1,
              scale: 1,
              duration: 1,
              ease: "power2.out",
            },
            index * 0.5
          ); // Stagger timing
        }
      });
    }, sectionRef);

    return () => ctx.revert();
  }, []);

  return (
    <section
      ref={sectionRef}
      className="bg-[#0a0a0a] text-white font-sansBody relative"
      style={{ height: "400vh" }}
    >
      {/* Pinned Text Section */}
      <div
        ref={textRef}
        className="h-screen flex flex-col justify-center items-center px-6 md:px-24 text-center relative z-10"
      >
        <p className="uppercase text-xs tracking-widest text-[#666666] mb-6 font-sansBody">
          ABOUT ME
        </p>
        <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold max-w-5xl leading-tight font-serifTitle">
          INDIA-BASED INDEPENDENT CREATIVE, SPECIALIZING IN HELPING BUSINESSES
          AND INDIVIDUALS TURN THEIR IDEAS INTO IMPACTFUL DEVELOPMENT AND DESIGN
          SOLUTIONS.
        </h2>
        <span className="text-2xl mt-6">✍️</span>
      </div>

      {/* Images that will scroll over the pinned text */}
      <div className="absolute inset-0 pointer-events-none">
        {/* First image - left side */}
        <img
          ref={(el) => (imagesRef.current[0] = el)}
          src="src/assets/images/about/a1.png"
          alt="Me 1"
          className="absolute left-[15%] top-1/2 w-[180px] h-[220px] object-cover rounded-lg shadow-2xl z-20"
        />

        {/* Second image - right side */}
        <img
          ref={(el) => (imagesRef.current[1] = el)}
          src="src/assets/images/about/a2.png"
          alt="Me 2"
          className="absolute right-[15%] top-1/2 w-[180px] h-[220px] object-cover rounded-lg shadow-2xl z-20"
        />

        {/* Third image - center */}
        <img
          ref={(el) => (imagesRef.current[2] = el)}
          src="src/assets/images/about/a3.png"
          alt="Me 3"
          className="absolute left-1/2 top-1/3 transform -translate-x-1/2 -translate-y-1/2 w-[200px] h-[200px] object-cover rounded-lg shadow-2xl z-20"
        />

        {/* Final large image - center bottom */}
        <img
          ref={(el) => (imagesRef.current[3] = el)}
          src="src/assets/images/about/a4.png"
          alt="Me 4"
          className="absolute left-1/2 bottom-1/4 transform -translate-x-1/2 w-[250px] h-[350px] object-cover rounded-lg shadow-2xl z-30"
        />
      </div>
    </section>
  );
}
