import React from "react";
import { GoArrowUpRight } from "react-icons/go";

export default function HeroSection() {
  return (
    <section className="min-h-screen bg-[#0a0a0a] text-white font-sansBody px-6 md:px-16 lg:px-24 py-10">
      {/* Main Title */}
      <div className="mt-20 text-left">
        <h1 className="text-[12vw] sm:text-[10vw] md:text-[8vw] lg:text-[6vw] xl:text-[5.5vw] font-serifTitle leading-[0.9] tracking-tight">
          <span className="text-white font-light">RAJ </span>
          <span className="text-white font-bold">CHHATWANI</span>
          <sup className="text-white text-[2vw] align-top ml-2">©</sup>
        </h1>
      </div>

      {/* Content Row */}
      <div className="mt-16 flex flex-col lg:flex-row items-start justify-between gap-12">
        {/* Left Side - Text Content */}
        <div className="flex-1 max-w-md">
          <p className="text-[#666666] text-base leading-relaxed font-sansBody">
            Made with hate
            <br />
            because perfection isn't born out of love.
            <br />
            It's forged in frustration, obsession, and an
            <br />
            unrelenting pursuit of something better.
          </p>
          <button className="mt-8 bg-[#FF6B47] hover:bg-[#e85a3e] text-white font-semibold py-3 px-8 rounded-full text-sm tracking-wide flex items-center gap-2 transition-all duration-300">
            BOOK A CALL
            <GoArrowUpRight className="text-lg" />
          </button>
        </div>

        {/* Right Side - Image and Date */}
        <div className="flex-1 flex flex-col items-end">
          <div className="relative">
            <img
              src="src/assets/images/heroimg.png"
              alt="Raj"
              className="w-80 h-80 object-cover rounded-lg"
            />
          </div>
          {/* Bottom Freelance Info */}
          <div className="mt-8 text-right text-[#666666] text-xs">
            <div className="uppercase tracking-wider">
              AVAILABLE FOR FREELANCE WORK
            </div>
            <div className="text-4xl font-bold text-white mt-2 font-serifTitle">
              APR ‘25
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
