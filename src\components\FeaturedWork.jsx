import React from "react";
import ProjectCard from "./card/ProjectCard";

const projects = [
  {
    title: "Tech Expo",
    subtitle: "Branding / Identity / UX",
    year: "2024",
    image: "src/assets/images/work/w1.png",
  },
  {
    title: "Honigdachs",
    subtitle: "UI/UX Design / Agency Website",
    year: "2024",
    image: "src/assets/images/work/w1.png",
  },
  {
    title: "Unboxing Community",
    subtitle: "App design / Consumer strategy",
    year: "2023",
    image: "src/assets/images/work/w1.png",
  },
  {
    title: "Motion Design",
    subtitle: "Motion / Micro Interaction",
    year: "2019",
    image: "src/assets/images/work/w1.png",
  },
  {
    title: "Neuramonks",
    subtitle: "UI/UX design / Motion Design",
    year: "2017",
    image: "src/assets/images/work/w1.png",
  },
];

export default function FeaturedWork() {
  return (
    <section className=" text-white font-sans px-6 md:px-16 lg:px-24 py-24">
      <div className="text-center mb-16 px-4">
        {/* Subtitle */}
        <p className="text-[#A1A1A1] text-xs md:text-sm tracking-widest uppercase">
          // THE HALL OF FAME //
        </p>

        {/* Title */}
        <h2 className="mt-2 text-[14vw] sm:text-[12vw] md:text-[10vw] lg:text-[8vw] xl:text-[7vw] font-serif leading-none">
          <span className="block">FEATURED</span>
          <div className="mt-4 flex justify-center items-center gap-4">
            {/* Line */}
            <div className="w-30 h-[10px] bg-[#FF6C4F]"></div>

            {/* WORK */}
            <span className="text-white text-[14vw] sm:text-[12vw] md:text-[10vw] lg:text-[8vw] xl:text-[7vw] font-extrabold tracking-tight">
              WORK
            </span>
          </div>
        </h2>
        {/* Line + WORK in one row, centered */}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
        <div className="flex flex-row justify-center items-center">
          <ProjectCard project={projects[0]} height="full" />
        </div>
        {/* Top Row: First Two Cards */}
        {projects.slice(0, 2).map((project, index) => (
          //if the index is 0 the height is lit bit larger than the index 1 and index 1 is in centre of the height of index 0

          <div
            key={index}
            className="bg-[#1B1B1B] rounded-lg border border-[#2C2C2C] overflow-hidden "
          >
            <div className="p-3">
              <img
                src={project.image}
                alt={project.title}
                className="rounded-md w-full h-auto object-cover"
              />
            </div>
            <div className="px-4 pb-4">
              <div className="flex justify-between items-center mb-1">
                <h3 className="text-white text-base font-medium">
                  {project.title}
                </h3>
                <p className="text-sm text-[#A1A1A1]">@{project.year}</p>
              </div>
              <p className="text-sm text-[#A1A1A1]">{project.subtitle}</p>
            </div>
          </div>
        ))}

        {/* Middle Large Card (Full Width) */}
        <div className="md:col-span-2">
          <div className="bg-[#1B1B1B] rounded-lg border border-[#2C2C2C] overflow-hidden">
            <div className="p-3">
              <img
                src={projects[2].image}
                alt={projects[2].title}
                className="rounded-md w-full h-auto object-cover"
              />
            </div>
            <div className="px-4 pb-4">
              <div className="flex justify-between items-center mb-1">
                <h3 className="text-white text-base font-medium">
                  {projects[2].title}
                </h3>
                <p className="text-sm text-[#A1A1A1]">@{projects[2].year}</p>
              </div>
              <p className="text-sm text-[#A1A1A1]">{projects[2].subtitle}</p>
            </div>
          </div>
        </div>

        {/* Bottom Row: Last Two Cards */}
        {projects.slice(3).map((project, index) => (
          <div
            key={index}
            className="bg-[#1B1B1B] rounded-lg border border-[#2C2C2C] overflow-hidden"
          >
            <div className="p-3">
              <img
                src={project.image}
                alt={project.title}
                className="rounded-md w-full h-auto object-cover"
              />
            </div>
            <div className="px-4 pb-4">
              <div className="flex justify-between items-center mb-1">
                <h3 className="text-white text-base font-medium">
                  {project.title}
                </h3>
                <p className="text-sm text-[#A1A1A1]">@{project.year}</p>
              </div>
              <p className="text-sm text-[#A1A1A1]">{project.subtitle}</p>
            </div>
          </div>
        ))}
      </div>

      <img src="src\assets\images\work\nuv.png" alt="Unboxing Community" />
    </section>
  );
}
