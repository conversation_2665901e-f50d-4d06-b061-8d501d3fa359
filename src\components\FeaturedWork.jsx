import React from "react";
import ProjectCard from "./card/ProjectCard";

const projects = [
  {
    title: "Tech Expo",
    subtitle: "Branding / Identity / UX",
    year: "2024",
    image: "src/assets/images/work/w1.png",
  },
  {
    title: "Honigdachs",
    subtitle: "UI/UX Design / Agency Website",
    year: "2024",
    image: "src/assets/images/work/w2.png",
  },
  {
    title: "Unboxing Community",
    subtitle: "App design / Consumer strategy",
    year: "2023",
    image: "src/assets/images/work/w3.png",
  },
  {
    title: "Motion Design",
    subtitle: "Motion / Micro Interaction",
    year: "2019",
    image: "src/assets/images/work/w4.png",
  },
  {
    title: "Neuramonks",
    subtitle: "UI/UX design / Motion Design",
    year: "2017",
    image: "src/assets/images/work/w5.png",
  },
];

export default function FeaturedWork() {
  return (
    <section className="bg-[#0a0a0a] text-white font-sansBody px-6 md:px-16 lg:px-24 py-24">
      <div className="text-center mb-20">
        {/* Title */}
        <h2 className="text-[12vw] sm:text-[10vw] md:text-[8vw] lg:text-[6vw] xl:text-[5vw] font-serifTitle leading-[0.9] tracking-tight">
          <span className="block font-light">FEATURED</span>
          <div className="mt-2 flex justify-center items-center gap-6">
            {/* Line */}
            <div className="w-16 h-1 bg-[#FF6B47]"></div>
            {/* WORK */}
            <span className="font-bold">WORK</span>
          </div>
        </h2>
      </div>

      {/* Grid Layout */}
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Row 1: Two cards side by side */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <ProjectCard project={projects[0]} height="medium" />
          <ProjectCard project={projects[1]} height="medium" />
        </div>

        {/* Row 2: Full width card */}
        <div>
          <ProjectCard project={projects[2]} height="wide" />
        </div>

        {/* Row 3: Two cards side by side */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <ProjectCard project={projects[3]} height="medium" />
          <ProjectCard project={projects[4]} height="medium" />
        </div>
      </div>
    </section>
  );
}
