// components/Navbar.jsx
import React, { useState } from "react";
import { FiMenu, FiX } from "react-icons/fi";

const Navbar = () => {
  return (
    <div className="flex justify-between items-center text-sm text-[#666666] font-sansBody py-4">
      <div className="flex gap-1 items-center font-bold text-white">
        <span className="font-serifTitle">By Raj</span>
        <sup className="text-xs">™</sup>
      </div>
      <span className="hidden md:block text-xs">
        (Product & Visual Designer)
      </span>
      <div className="hidden sm:flex gap-6 text-xs">
        <a href="#" className="hover:text-white transition-colors">
          Services
        </a>
        <a href="#" className="hover:text-white transition-colors">
          Works
        </a>
        <a href="#" className="hover:text-white transition-colors">
          About
        </a>
        <a href="#" className="hover:text-white transition-colors">
          Testimonials
        </a>
        <a href="#" className="hover:text-white transition-colors">
          Contact
        </a>
      </div>
    </div>
  );
};

export default Navbar;
