import React from "react";

const Hero = () => {
  return (
    <main className="mt-20 flex flex-col md:flex-row items-center justify-between gap-10">
      <div className="flex-1">
        <h1 className="text-4xl sm:text-5xl md:text-6xl font-light">
          RAJ <span className="font-extrabold">CHHATWANI</span> <span className="text-3xl">©</span>
        </h1>

        <div className="mt-6 text-gray-400 max-w-md text-sm md:text-base">
          <p className="mb-6">
            Made with hate<br />
            because perfection isn't born out of love.<br />
            It's forged in frustration, obsession, and an<br />
            unrelenting pursuit of something better.
          </p>

          <button className="bg-orange-500 text-white px-5 py-2 rounded-full text-sm font-semibold hover:bg-orange-600 transition">
            BOOK A CALL ↗
          </button>
        </div>
      </div>

      <div className="flex flex-col items-center">
        <img
          src="https://i.imgur.com/1NqZ7Kb.jpg" // Replace with your image
          alt="Raj"
          className="w-72 h-72 object-cover rounded-md"
        />
        <div className="mt-6 text-center text-gray-400 text-xs sm:text-sm">
          <p>AVAILABLE FOR FREELANCE WORK</p>
          <p className="text-3xl sm:text-4xl font-bold text-white mt-1">APR ‘25</p>
        </div>
      </div>
    </main>
  );
};

export default Hero;
