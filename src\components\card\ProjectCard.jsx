const ProjectCard = ({ project, height = "auto" }) => {
  const heightClasses = {
    medium: "h-[350px]",
    wide: "h-[400px]",
    tall: "h-[500px]",
    auto: "h-auto",
  };

  const imageHeightClasses = {
    medium: "h-[250px]",
    wide: "h-[300px]",
    tall: "h-[400px]",
    auto: "h-auto",
  };

  return (
    <div
      className={`bg-[#1a1a1a] rounded-lg overflow-hidden transition-all duration-300 hover:scale-[1.02] group ${heightClasses[height]}`}
    >
      <div className="p-4 h-full flex flex-col">
        <div className="flex-1 mb-4">
          <img
            src={project.image}
            alt={project.title}
            className={`rounded-lg w-full object-cover group-hover:scale-105 transition-transform duration-300 ${imageHeightClasses[height]}`}
          />
        </div>
        <div className="space-y-1">
          <div className="flex justify-between items-start">
            <h3 className="text-white text-base font-medium font-sansBody">
              {project.title}
            </h3>
            <span className="text-xs text-[#666666]">@{project.year}</span>
          </div>
          <p className="text-sm text-[#666666] font-sansBody">
            {project.subtitle}
          </p>
        </div>
      </div>
    </div>
  );
};

export default ProjectCard;
