const ProjectCard = ({ project, height = "auto" }) => {
  const heightClasses = {
    full: "h-[660.93px]",
    short: "h-[600px]",
    auto: "h-auto",
  };

  return (
    <div
      key={project}
      className={`bg-[#1B1B1B] rounded-lg border border-[#2C2C2C] overflow-hidden ${heightClasses[height]} `}
    >
      <div className="p-3">
        <img
          src={project.image}
          alt={project.title}
          className="rounded-md w-full h-auto object-cover"
        />
      </div>
      <div className="px-4 pb-4">
        <div className="flex justify-between items-center mb-1">
          <h3 className="text-white text-base font-medium">{project.title}</h3>
          <p className="text-sm text-[#A1A1A1]">@{project.year}</p>
        </div>
        <p className="text-sm text-[#A1A1A1]">{project.subtitle}</p>
      </div>
    </div>
    // <div
    //   className={`bg-[#1B1B1B] rounded-md overflow-hidden ${heightClasses[height]}`}
    // >
    //   <img
    //     src={project.image}
    //     alt={project.title}
    //     className="w-full h-3/5 object-cover"
    //   />
    //   <div className="p-4">
    //     <h3 className="text-lg font-semibold mb-1">{project.title}</h3>
    //     <p className="text-sm text-[#A1A1A1]">{project.subtitle}</p>
    //     <p className="text-sm text-[#A1A1A1] mt-2">@{project.year}</p>
    //   </div>
    // </div>
  );
};

export default ProjectCard;
